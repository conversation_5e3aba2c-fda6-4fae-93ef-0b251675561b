/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.base.client.dto.request.AccountRequest;
import io.quarkus.test.junit.QuarkusTest;
import io.smallrye.mutiny.helpers.test.UniAssertSubscriber;
import jakarta.inject.Inject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Test class demonstrating the improved testability of the refactored UserValidationService.
 * This shows how the separation of concerns makes testing much simpler and more focused.
 */
@QuarkusTest
class UserValidationServiceTest {

    @Inject
    UserValidationService userValidationService;

    @Test
    @DisplayName("Should normalize email to lowercase and trim whitespace")
    void shouldNormalizeEmailCorrectly() {
        // Given
        String inputEmail = "  <EMAIL>  ";
        
        // When
        String result = userValidationService.validateAndNormalizeEmail(inputEmail);
        
        // Then
        assertEquals("<EMAIL>", result);
    }

    @Test
    @DisplayName("Should throw exception for null email")
    void shouldThrowExceptionForNullEmail() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> 
            userValidationService.validateAndNormalizeEmail(null)
        );
    }

    @Test
    @DisplayName("Should throw exception for blank email")
    void shouldThrowExceptionForBlankEmail() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> 
            userValidationService.validateAndNormalizeEmail("   ")
        );
    }

    @Test
    @DisplayName("Should throw exception for invalid email format")
    void shouldThrowExceptionForInvalidEmailFormat() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> 
            userValidationService.validateAndNormalizeEmail("invalid-email")
        );
    }

    @Test
    @DisplayName("Should validate that user has required information")
    void shouldValidateRequiredUserInformation() {
        // Given
        AccountRequest request = AccountRequest.builder()
                .username("<EMAIL>")
                .firstName("John")
                .lastName("Doe")
                .properties(new HashMap<>())
                .build();
        
        // When
        boolean result = userValidationService.hasRequiredUserInformation(request);
        
        // Then
        assertTrue(result);
    }

    @Test
    @DisplayName("Should return false when required information is missing")
    void shouldReturnFalseWhenRequiredInformationMissing() {
        // Given - missing firstName
        AccountRequest request = AccountRequest.builder()
                .username("<EMAIL>")
                .lastName("Doe")
                .properties(new HashMap<>())
                .build();
        
        // When
        boolean result = userValidationService.hasRequiredUserInformation(request);
        
        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("Should return false when fields are blank")
    void shouldReturnFalseWhenFieldsAreBlank() {
        // Given - blank firstName
        AccountRequest request = AccountRequest.builder()
                .username("<EMAIL>")
                .firstName("   ")
                .lastName("Doe")
                .properties(new HashMap<>())
                .build();
        
        // When
        boolean result = userValidationService.hasRequiredUserInformation(request);
        
        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("Should validate account request successfully for valid input")
    void shouldValidateAccountRequestSuccessfully() {
        // Given
        Map<String, Object> properties = new HashMap<>();
        properties.put("department", "Engineering");
        
        AccountRequest request = AccountRequest.builder()
                .username("<EMAIL>")
                .firstName("John")
                .lastName("Doe")
                .properties(properties)
                .build();
        
        String tenantId = "test-tenant";
        
        // When
        UniAssertSubscriber<Void> subscriber = userValidationService
                .validateAccountRequest(tenantId, request)
                .subscribe().withSubscriber(UniAssertSubscriber.create());
        
        // Then
        subscriber.assertCompleted();
    }

    /**
     * This test demonstrates how easy it is to test individual components
     * after the refactoring. Before the refactoring, testing validation logic
     * required setting up the entire user creation workflow.
     */
    @Test
    @DisplayName("Should demonstrate improved testability compared to original monolithic approach")
    void shouldDemonstrateImprovedTestability() {
        // Before refactoring: To test email validation, you would need to:
        // 1. Mock EntityServiceClient
        // 2. Mock SecurityProviderServiceFactory
        // 3. Mock RoleRepository
        // 4. Mock UserGroupService
        // 5. Mock CacheService
        // 6. Mock AsyncConfigurationService
        // 7. Mock NotificationService
        // 8. Set up complex test data for the entire workflow
        // 9. Run the entire create() method just to test email normalization
        
        // After refactoring: To test email validation, you only need:
        String email = "<EMAIL>";
        String normalized = userValidationService.validateAndNormalizeEmail(email);
        assertEquals("<EMAIL>", normalized);
        
        // This demonstrates:
        // - Single Responsibility Principle: UserValidationService only handles validation
        // - Easy testing: No complex setup required
        // - Fast tests: No need to mock unrelated dependencies
        // - Clear intent: Test name and implementation clearly show what's being tested
    }
}
